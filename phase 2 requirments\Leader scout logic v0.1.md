# Leader scout matching system

# Applicant Side

### Tags matching 

1. The ai have multi tags like (Education, Experience, Skills,Achievements,etc)  
2. Under each tag there is subtags like in education (GPA , university, graduation project,etc)  
3. The  Ai agent will Collect as much Tags data as possible from the CV  
4. Choosing a suitable number of Questions ( every category need to be filled with at least 70% ( review this after deciding the Tags )  
5. Generate questions that are related to the filled tags to fill the Related Tags  
6.  Extract Tags data from the answers 

TODO: find the catches on the CV

### Description matching

1. The Ai will combine all the answers with the CV in a template format   
   	TODO: Create the template  
2. Store the template embedding on a vector database

# Recruiter Side

### Tags matching

1. Fill as many Tags as possible from the recruiter query   
2. Sort the applicants by matching with the Tags   
3. Give scoring based on matching with the recruiter query Tags

### Description matching 

1. Embed the user query   
2. Sort the tags matches by the embedding matching 

## Scoring system follow the document: 

[https://docs.google.com/document/d/13dMHmnbdZ1HWVzyqN5vF2Z7YwKgTfwfX/edit](https://docs.google.com/document/d/13dMHmnbdZ1HWVzyqN5vF2Z7YwKgTfwfX/edit)

