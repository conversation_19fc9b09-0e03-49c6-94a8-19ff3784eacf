# **Leader Scout Phase 2** 

## **Phase 2 Objectives**

### **1\. Enhanced Scoring System**

* **Smarter AI Evaluation**: Improved logic for analyzing CVs and interview responses  
* **Customizable Weights**: Recruiters can adjust scoring criteria per job (e.g., prioritize experience over education for senior roles)  
* **Human \+ AI Scoring**: Combine automated scores with recruiter assessments for better accuracy  
* **New scoring matrix :** Following the matrix at this [**document**](https://docs.google.com/document/d/13dMHmnbdZ1HWVzyqN5vF2Z7YwKgTfwfX/edit) 

### **2\. Recruiter Dashboard**

* **Candidate Management**: Filter, sort, and manage applicants by score, experience level, or status  
* **Detailed Profiles**: View complete candidate breakdowns including CV analysis and interview responses  
* **Custom Scoring Templates**: Save and reuse scoring preferences for different job types  
* **Bulk Actions**: Shortlist, reject, or tag multiple candidates simultaneously

### **3\. Admin Control Panel**

* **Platform Management**: Control default scoring templates and system-wide settings  
* **User Management**: Manage recruiter accounts and permissions  
* **Analytics**: Track platform usage, success rates, and performance metrics  
* **Template Management**: Create and maintain scoring templates for different industries

### **4\. Quiz & Assessment System**

* **Custom Quizzes**: Recruiters can send skill-specific tests to candidates  
* **Psychometric Tests**: Automated personality and aptitude assessments via email  
* **Flexible Scoring**: Integrate quiz results into final candidate scores  
* **Third-party Integration**: Support external assessment providers

### **5\. UI/UX Enhancement**

* **Professional Dashboard Design**: Clean, intuitive interface based on Figma mockups  
* **Mobile Responsiveness**: Optimized experience across all devices  
* **Advanced Search & Filtering**: Multi-parameter search with saved filters and quick-access buttons  
* **Exporting :**  One-click export to Excel with top candidates   
* **Pop-up chat :** enhancing the pop-up chat to be user friendly during the interview

### **6\. Advanced Interview System**

The AI interview will includes:

* **Dynamic Question Flow**: 15 structured questions across 5 stages  
* **Experience-Based Adaptation**: Questions adjust based on candidate seniority (fresh/junior/mid/senior)  
* **Red Flag Detection**: Additional questions for employment gaps or frequent job changes  
* **Follow-up Intelligence**: AI asks clarifying questions for incomplete responses

