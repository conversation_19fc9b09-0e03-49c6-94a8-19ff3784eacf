<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Animation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 50px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }
        
        .animation-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
        
        /* Upload animation keyframes */
        @keyframes uploadPulse {
          0%, 100% {
            opacity: 0.7;
            transform: scale(1);
          }
          50% {
            opacity: 1;
            transform: scale(1.15);
          }
        }

        @keyframes uploadArrow {
          0% {
            transform: translateY(6px);
            opacity: 0.4;
          }
          25% {
            transform: translateY(3px);
            opacity: 0.7;
          }
          50% {
            transform: translateY(0px);
            opacity: 1;
          }
          75% {
            transform: translateY(-3px);
            opacity: 0.7;
          }
          100% {
            transform: translateY(-6px);
            opacity: 0.4;
          }
        }

        .upload-animation-svg {
          animation: uploadPulse 1.5s ease-in-out infinite !important;
          transform-origin: center center;
        }

        .upload-animation-arrow {
          animation: uploadArrow 1.2s ease-in-out infinite !important;
          transform-origin: center center;
        }
        
        button {
            background: linear-gradient(135deg, #4169e1 0%, #5a7bff 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        
        button:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>Upload Animation Test</h2>
        <p>Testing the uploadPulse and uploadArrow animations</p>
        
        <div class="animation-container">
            <svg class="upload-animation-svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#4169e1" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline class="upload-animation-arrow" points="7,10 12,5 17,10"></polyline>
                <line class="upload-animation-arrow" x1="12" y1="5" x2="12" y2="15"></line>
            </svg>
        </div>
        
        <p><strong>uploadPulse:</strong> The entire SVG should scale and change opacity</p>
        <p><strong>uploadArrow:</strong> The arrow elements should move upward continuously</p>
        
        <button onclick="testUploadAnimation()">Test Upload Animation</button>
        <button onclick="location.href='static/footer.html'">Open Chat</button>
    </div>

    <script>
        function testUploadAnimation() {
            alert('The animations should be running continuously. The SVG should pulse (scale + opacity) and the arrow elements should move upward in a loop.');
        }
    </script>
</body>
</html>
