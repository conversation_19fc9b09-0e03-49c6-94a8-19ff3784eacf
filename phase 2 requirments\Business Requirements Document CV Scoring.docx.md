**Business Requirements Document (BRD)**

**Project: Candidate Scoring System**

---

**Purpose**

To enable recruiters to evaluate candidates using a combination of AI-powered CV analysis and human assessment inputs. The system supports a default candidate scoring template (used platform-wide) and allows recruiter-specific customization per job post. Final candidate scores combine AI and human components for more accurate and holistic evaluation.

---

**1\. AI Candidate Scoring**

---

**1.1 General Candidate Scoring Template (Default)**

* Created and maintained by the platform administrator.

* Used as a fallback when a recruiter does not define a custom scoring method.

* Applied across all jobs unless overridden by job-specific settings.

**Default Scoring Weights:**

* Education: 20%

* Experience: 25%

* Skills: 20%

* Achievements: 10%

* Certifications: 10%

* Formatting (Grammar/Layout): 5%

* Recruiter Bot Questions: 10%

These weights are applied to parsed data from the candidate’s CV and answers submitted via the Q\&A bot.

---

**1.2 Job-Specific Candidate Scoring Template**

* Recruiters may override the default weights for any job.

* They can also save custom templates for future reuse.

* A user interface will allow drag-and-drop sliders or dropdowns to define the custom weights.

**Example – Tech Role:**

* Education: 10%

* Experience: 30%

* Skills: 35%

* Certifications: 10%

* Recruiter Questions: 15%

---

**1.3 AI Scoring Logic Example**

| Component | Max Points | Candidate Score | Weight | Weighted Score |
| :---- | :---- | :---- | :---- | :---- |
| Education | 100 | 80 | 30% | 24 |
| Experience | 100 | 70 | 20% | 14 |
| Skills Match | 100 | 90 | 25% | 22.5 |
| Certifications | 100 | 50 | 15% | 7.5 |
| Recruiter Questions | 100 | 100 | 10% | 10 |
| **Total** |  |  |  | **78/100** |

---

**2\. Human Scoring**

---

**2.1 Components of Human Scoring**

| Input Type | Description |
| :---- | :---- |
| **Case Study** | Manual score entry by recruiter (0–100 scale). |
| **Psychometric Test** | Sent to candidate by email, completed online, auto-scored via API/Webhook. |
| **Background Check** | Status-based field: “Clean”, “Pending”, or “Flagged”. |

---

**2.2 Psychometric Test Email Integration**

* Trigger: When a candidate completes their CV submission or Q\&A.

* Action: The system sends a psychometric test link to the candidate’s email.

* Candidates complete the test via a third-party provider or internal module.

* Results are fetched via webhook/API or manually uploaded by recruiter.

* Scored categories may include:

  * Logical Reasoning

  * Teamwork

  * Leadership

  * Stress Tolerance

* Each trait is mapped to a score (e.g., High \= 10, Medium \= 7, Low \= 4), then weighted in the total Human Score.

**Example Mapping:**

json

CopyEdit

"psychometric": {

  "logical": "high",       // 10 points

  "teamwork": "medium",    // 7 points

  "leadership": "low"      // 4 points

}

---

**2.3 Human Scoring Breakdown Example**

| Assessment | Score | Weight |
| :---- | :---- | :---- |
| Case Study | 80 | 50% |
| Psychometric Test | 70 | 30% |
| Background Check | 100 | 20% |
| **Human Score** |  | **80** |

---

**3\. Final Candidate Score Calculation**

---

* Final Score \= (AI Score × AI Weight) \+ (Human Score × Human Weight)

* Weighting ratio is **configurable per job** (default is 60% AI, 40% Human)

**Example Calculation:**

* AI Score \= 78

* Human Score \= 80

* Ratio \= 60% AI / 40% Human

* Final Score \= (78 × 0.6) \+ (80 × 0.4) \= **78.8**

---

**4\. Recruiter Dashboard**

---

**4.1 Candidate List View**

| Candidate Name | AI Score | Human Score | Final Score | Status |
| :---- | :---- | :---- | :---- | :---- |
| John Smith | 78 | 80 | 78.8 | Shortlisted |
| Aisha Khan | 88 | 50 | 73.6 | Under Review |
| Liu Zhang | 92 | 92 | 92 | Top Candidate |

---

**4.2 Expanded Candidate View**

* AI Scoring Breakdown (per section)

* Human Inputs (case study file, psychometric score, background status)

* Recruiter Notes and Feedback

* Uploaded Documents (PDF CV, Test Reports)

---

**5\. Functional Requirements**

---

* Admin can define and edit the global candidate scoring template.

* Recruiters can customize weights per job (or use default).

* The system sends a psychometric test link via email.

* The system tracks completion and fetches results via API/webhook.

* Recruiter can manually enter case study results and background status.

* Combined score is calculated using configurable AI/Human ratio.

* Dashboard displays candidates, scores, statuses, and filters (e.g., score \> 80).

* Recruiters can sort candidates by Final Score, filter by status, or bulk tag.

---

**6\. Data Model (Simplified)**

---

**CandidateScoringTemplate**

json

CopyEdit

{

  "template\_id": "default",

  "components": {

    "education": 20,

    "experience": 25,

    "skills": 20,

    "achievements": 10,

    "certifications": 10,

    "formatting": 5,

    "recruiter\_questions": 10

  }

}

---

**CandidateScore**

json

CopyEdit

{

  "candidate\_id": "abc123",

  "job\_id": "job456",

  "ai\_score": 78,

  "human\_score": 80,

  "final\_score": 78.8,

  "details": {

    "education": 80,

    "experience": 70,

    "skills": 90,

    "recruiter\_questions": 100

  },

  "human\_inputs": {

    "case\_study": 80,

    "psychometric": {

      "logical": "high",

      "teamwork": "medium"

    },

    "background\_check": "clean"

  }

}

