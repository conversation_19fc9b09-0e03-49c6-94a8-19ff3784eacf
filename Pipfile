[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = {extras = ["standard"], version = "<0.116.0,>=0.115.0"}
uvicorn = "<0.35.0,>=0.34.0"
pydantic = "<2.12.0,>=2.11.0"
python-multipart = ">=0.0.20"
openai = ">=1.0.0"
python-dotenv = ">=1.0.0"
supabase = ">=2.0.0"
langchain = ">=0.1.14"
langchain-community = ">=0.0.32"
langchain-core = ">=0.1.34"
langchain-openai = ">=0.1.6"
pinecone = ">=3.0.0"
tiktoken = ">=0.6.0"
beautifulsoup4 = ">=4.12.3"
requests = ">=2.31.0"
packaging = ">=23.0"
langchain-pinecone = ">=0.1.0"
sendgrid = ">=6.10.0"
google-api-python-client = ">=2.100.0"
google-auth-httplib2 = ">=0.1.0"
google-auth-oauthlib = ">=1.1.0"
twilio = ">=8.0.0"
PyPDF2 = "*"
reportlab = "*"
python-docx = "*"
google-generativeai = "*"

[dev-packages]

[requires]
python_version = "3.13.5"
