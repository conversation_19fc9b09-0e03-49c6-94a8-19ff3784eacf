import logging
from langchain.tools import tool
from database.models import LeadInfo
from services import service_init, lead_service

logger = logging.getLogger("save_lead_tool")


def create_tool(agent):
    """Return a lead-saving tool bound to the given agent instance."""

    @tool
    def save_lead(
        name: str,
        email: str = None,
        phone: str = None,
        lead_type: str = None,
        company: str = None,
        industry: str = None,
        num_locations: int = None,
        num_employees: int = None,
        additional_message: str = None,
    ) -> str:
        """Save lead information to the database and trigger follow-up actions.

        Args:
            name: Lead's name (required)
            email: Lead's email address
            phone: Lead's phone number
            lead_type: Type of lead ('company' or 'recruitment_partner')
            company: Lead's company name
            industry: Lead's industry
            num_locations: Number of business locations
            num_employees: Number of employees
            additional_message: Any additional message or notes

        Returns:
            Success message or error message.
        """
        try:
            # Validate required information
            if not name or name.lower() in ["unknown", "customer", ""]:
                return "I need a valid name to save your information."

            if not email and not phone:
                return "I need either an email address or phone number to contact you."

            if not agent.session_id:
                return "Session information is missing. Please try again."

            # Create LeadInfo object
            lead_info = LeadInfo(
                name=name,
                email=email,
                phone=phone,
                lead_type=lead_type,
                company=company,
                industry=industry,
                num_locations=num_locations,
                num_employees=num_employees,
                additional_message=additional_message,
                consent_given=True,  # Assume consent when they provide info
            )

            # Get external services
            email_service = service_init.get_email_service()
            google_sheets_service = service_init.get_google_sheets_service()
            whatsapp_service = service_init.get_whatsapp_service()
            service_executor = service_init.get_service_executor()

            # Save the lead using the current session_id
            result = lead_service.save_lead(
                supabase_client=agent.supabase_client,
                lead_info=lead_info,
                session_id=agent.session_id,
                user_language="English",  # Default to English; agent detects actual language
                email_service=email_service,
                google_sheets_service=google_sheets_service,
                whatsapp_service=whatsapp_service,
                service_executor=service_executor,
            )

            if result:
                return f"Thanks {name}! Your information has been saved and someone from our team will contact you soon."
            return "There was an issue saving your information. Please try again or contact our support team."

        except Exception as exc:
            logger.error("Error in save_lead: %s", exc)
            return "I encountered an error while saving your information. Please try again or contact our support team."

    return save_lead
