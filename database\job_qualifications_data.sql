-- Job Qualifications Category and Tag Definition
-- Add job_qualifications category and professional_qualifications tag
-- Run this after the main database setup to add the job qualifications feature
-- ================================================

-- Insert job_qualifications category
INSERT INTO tag_categories (name, display_name, description, default_threshold, priority) VALUES
('job_qualifications', 'Job Qualifications', 'Professional qualifications, licenses, certifications, and requirements specific to the candidate''s field', 0.80, 0)
ON CONFLICT (name) DO NOTHING;

-- Insert professional_qualifications tag definition
INSERT INTO tag_definitions (category_id, tag_key, display_name, data_type, extraction_hints, question_templates, weight)
SELECT id, 'professional_qualifications', 'Professional Qualifications', 'text',
       ARRAY['license', 'certification', 'qualified', 'certified', 'degree', 'diploma', 'accredited', 'registered', 'board certified'],
       ARRAY['Do you have the required professional qualifications for this role?', 'What licenses or certifications do you hold?'],
       1.0  -- Maximum weight within precision limits
FROM tag_categories
WHERE name = 'job_qualifications'
ON CONFLICT (category_id, tag_key) DO NOTHING;
