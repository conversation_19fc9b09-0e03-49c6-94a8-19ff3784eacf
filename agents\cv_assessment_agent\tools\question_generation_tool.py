"""Question Generation Tool for CV Assessment Agent

Handles intelligent question generation based on CV analysis, gap identification,
and smart distribution of questions across categories.

IMPROVEMENTS MADE:
- Consolidated all question generation to use a single, consistent prompt
- Removed redundant questions about information already in CV
- Eliminated requests for exact months when years are sufficient
- Removed unnecessary explanations in questions (e.g., "This will help us understand...")
- Ensured questions are direct and concise
- Added validation to prevent asking about already-covered topics
"""

import re
import json
import logging
import os
from typing import Optional, Dict, Any, List, Tuple
from uuid import UUID
from decimal import Decimal
from langchain.tools import tool
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()
from database.models import (
    TagCategory,
    TagDefinition,
    CandidateTag,
    CandidateTagCoverage,
    TagDataType,
    TagSource,
    JobLevel,
)
from services.tag_extraction_service import TagExtractionService

logger = logging.getLogger("question_generation_tool")


class QuestionGenerationService:
    """Service for generating intelligent questions based on CV analysis and gap identification"""

    def __init__(
        self,
        db_service,
        tag_extraction_service=None,
        model_name: str = "gemini-2.5-pro",
    ):
        """Initialize the question generation service"""
        logger.info("Initializing QuestionGenerationService with Gemini")
        self.db_service = db_service

        # Configure Gemini
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError(
                "GEMINI_API_KEY or GOOGLE_API_KEY environment variable is required"
            )

        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)

        # Default question limit
        self.default_question_limit = 15

        # Default category weights (based on BRD and optimized for relevance)
        self.default_category_weights = {
            "education": 0.25,
            "experience": 0.40,
            "skills": 0.25,
            "achievements": 0.10,
            "personal": 0.00,
            "cultural": 0.00,
            "certifications": 0.00,  # Only ask if specifically relevant to the job
            "formatting": 0.00,  # Not relevant for most assessments
            "recruiter_questions": 0.00,  # Only include if specifically requested
            "salary": 0.00,  # Only include if specifically requested
            "job_qualifications": 0.00,  # Handled separately as mandatory first question
        }

        # Initialize tag extraction service if not provided
        if tag_extraction_service:
            self.tag_extraction_service = tag_extraction_service
        else:
            self.tag_extraction_service = TagExtractionService(db_service, model_name)

        # OPTIMIZATION: Request-level cache to eliminate redundant database queries
        self._request_cache = {
            "candidate_tags": {},  # cv_assessment_id -> tags data
            "cv_assessments": {},  # assessment_id -> assessment data
        }

    async def generate_questions(
        self,
        cv_assessment_id: UUID,
        job_description: Optional[str] = None,
        question_limit: Optional[int] = None,
        category_weights: Optional[Dict[str, float]] = None,
    ) -> Dict[str, Any]:
        """
        Generate intelligent questions based on CV analysis and gap identification.

        Args:
            cv_assessment_id: The assessment ID
            job_description: Optional job description for context
            question_limit: Maximum number of questions to generate (default: 15)
            category_weights: Optional custom weights for categories

        Returns:
            Dictionary with generated questions and metadata
        """
        try:
            # OPTIMIZATION: Clear request cache for new question generation request
            self._clear_request_cache()

            logger.info(
                f"QuestionGenerationService: Starting question generation for assessment {cv_assessment_id}"
            )
            # Get proper assessment length using centralized logic
            if question_limit:
                limit = question_limit
                logger.info(f"Using provided question limit: {limit}")
            else:
                # Use tag extraction service to get proper assessment length
                limit = await self.tag_extraction_service.get_assessment_length(
                    cv_assessment_id
                )
                logger.info(f"Using assessment length from configuration: {limit}")

            # Get job-specific information if available
            job_id = None
            job_specific_weights = None
            assessment_data = self.db_service.get_cv_assessment_by_id(
                int(cv_assessment_id)
            )

            if assessment_data and assessment_data.get("job_id"):
                job_id = assessment_data["job_id"]
                job_data = self.db_service.get_job(job_id)

                if job_data:
                    # Check for job-specific weights
                    if job_data.get("custom_weights"):
                        job_specific_weights = job_data["custom_weights"]
                    elif job_data.get("template_id"):
                        template = self.db_service.get_scoring_template(
                            job_data["template_id"]
                        )
                        if template and template.get("category_weights"):
                            job_specific_weights = template["category_weights"]

            # Determine final category weights
            weights = (
                category_weights
                or job_specific_weights
                or self.default_category_weights
            )

            logger.info(f"Using category weights: {weights}")
            logger.info(
                f"Weight source: {'custom' if category_weights else 'job-specific' if job_specific_weights else 'default'}"
            )

            # Analyze coverage gaps
            logger.info(
                f"QuestionGenerationService: Analyzing coverage gaps for assessment {cv_assessment_id}"
            )
            gap_analysis = await self.tag_extraction_service.analyze_gaps(
                cv_assessment_id
            )

            if not gap_analysis or "priority_categories" not in gap_analysis:
                logger.warning(
                    "QuestionGenerationService: No gap analysis available - using default question distribution"
                )
                return await self._generate_default_questions(
                    cv_assessment_id, job_description, limit, weights
                )

            # OPTIMIZATION: Generate ALL questions in single API call
            return await self._generate_all_questions_single_call(
                cv_assessment_id, gap_analysis, job_description, limit, weights
            )

        except Exception as e:
            logger.error(f"Failed to generate questions: {str(e)}")
            return {"questions": [], "error": str(e), "success": False}

    async def _generate_all_questions_single_call(
        self,
        cv_assessment_id: UUID,
        gap_analysis: Dict[str, Any],
        job_description: Optional[str] = None,
        question_limit: int = 15,
        category_weights: Dict[str, float] = None,
    ) -> Dict[str, Any]:
        """
        OPTIMIZED: Generate ALL questions in a single API call instead of 4 separate calls.
        This reduces question generation time from 90+ seconds to 8-12 seconds.
        """
        try:
            # Calculate question distribution
            weights = category_weights or self.default_category_weights

            # Reserve 1 question for job qualifications, distribute the rest among categories
            category_question_limit = (
                question_limit - 1
            )  # Reserve 1 for job qualifications
            question_distribution = self._calculate_question_distribution(
                gap_analysis, weights, category_question_limit
            )

            logger.info(
                f"Generating ALL {question_limit} questions in single API call (1 job qualifications + {category_question_limit} category questions)"
            )
            logger.info(f"Category question distribution: {question_distribution}")

            # OPTIMIZATION: Use tag extraction service cache if available
            if hasattr(self.tag_extraction_service, "_request_cache"):
                cache_key = str(cv_assessment_id)
                if (
                    cache_key
                    in self.tag_extraction_service._request_cache["candidate_tags"]
                ):
                    existing_tags = self.tag_extraction_service._request_cache[
                        "candidate_tags"
                    ][cache_key]
                    logger.info(
                        f"Using tag extraction service cache for candidate tags"
                    )
                else:
                    existing_tags = self._get_cached_candidate_tags(cv_assessment_id)
            else:
                existing_tags = self._get_cached_candidate_tags(cv_assessment_id)

            already_extracted_tags = []

            if existing_tags:
                for tag in existing_tags:
                    tag_def = tag.get("tag_definitions", {})
                    tag_key = tag_def.get("tag_key", "")
                    display_name = tag_def.get("display_name", "")
                    tag_value = tag.get("value", "")
                    if tag_key and tag_value:
                        already_extracted_tags.append(
                            f"{tag_key} ({display_name}): {tag_value}"
                        )

            # Get current assessment data for Q&A history
            assessment_data = self._get_cached_cv_assessment(cv_assessment_id)
            qa_history = []
            if assessment_data and assessment_data.get("assessment_state"):
                try:
                    state_data = json.loads(assessment_data["assessment_state"])
                    if isinstance(state_data, dict):
                        qa_history = state_data.get("qa_history", [])
                    elif isinstance(state_data, list):
                        qa_history = state_data
                except:
                    qa_history = []

            # Build comprehensive prompt for all categories
            all_categories_info = []
            total_questions_needed = (
                question_limit  # Total includes job qualifications + category questions
            )

            for category_name, num_questions in question_distribution.items():
                if num_questions <= 0:
                    continue

                # Get missing tags for this category
                missing_tags = gap_analysis.get("missing_tags", {}).get(
                    category_name, []
                )

                if missing_tags:
                    tag_keys = [
                        f"{tag['tag_key']} ({tag['display_name']})"
                        for tag in missing_tags
                    ]
                    all_categories_info.append(
                        f"{category_name.upper()} ({num_questions} questions):\n"
                        f"Target tags: {', '.join(tag_keys)}"
                    )

            # Get CV text for better context
            cv_text = ""
            if assessment_data:
                cv_text = assessment_data.get("pdf_text", "")

            # Verify distribution matches category question limit exactly
            total_distributed = sum(question_distribution.values())
            if total_distributed != category_question_limit:
                logger.warning(
                    f"Category distribution mismatch: {total_distributed} != {category_question_limit}, adjusting..."
                )
                question_distribution = self._adjust_distribution_to_limit(
                    question_distribution, category_question_limit
                )

            # Build optimized prompt with CV text
            prompt = self._build_optimized_single_call_prompt(
                all_categories_info,
                already_extracted_tags,
                qa_history,
                job_description,
                total_questions_needed,
                question_distribution,
                cv_text,
            )

            # Make single API call with retry logic
            max_retries = 2
            all_questions = []

            for attempt in range(max_retries + 1):
                try:
                    logger.info(
                        f"Making API call attempt {attempt + 1}/{max_retries + 1}"
                    )
                    response = self.model.generate_content(prompt)

                    # Parse response to extract all questions
                    all_questions = self._parse_single_call_response(
                        response.text, question_distribution
                    )

                    # Check if we got enough questions
                    if (
                        len(all_questions) >= question_limit * 0.8
                    ):  # At least 80% of expected
                        break
                    elif attempt < max_retries:
                        logger.warning(
                            f"Got {len(all_questions)}/{question_limit} questions, retrying..."
                        )
                        continue

                except Exception as e:
                    logger.error(f"API call attempt {attempt + 1} failed: {str(e)}")
                    if attempt == max_retries:
                        raise

            # If we still don't have enough questions, pad with generic ones
            if len(all_questions) < question_limit:
                logger.warning(
                    f"Padding questions: got {len(all_questions)}, need {question_limit}"
                )
                all_questions = self._pad_questions_to_limit(
                    all_questions, question_limit, question_distribution
                )

            # Ensure we don't exceed the limit
            all_questions = all_questions[:question_limit]

            # Organize questions by category for return value
            questions_by_category = {}
            question_index = 0

            for category_name, num_questions in question_distribution.items():
                if num_questions <= 0:
                    continue

                category_questions = all_questions[
                    question_index : question_index + num_questions
                ]
                questions_by_category[category_name] = category_questions
                question_index += num_questions

            logger.info(
                f"Single API call completed - generated {len(all_questions)} questions (target: {question_limit})"
            )

            # Log each question with its category for debugging
            for i, question in enumerate(all_questions, 1):
                category = question.get("category", "unknown")
                tag_key = question.get("tag_key", "unknown")
                question_text = (
                    question.get("question", "")[:60] + "..."
                    if len(question.get("question", "")) > 60
                    else question.get("question", "")
                )
                logger.info(
                    f"Question {i} ({category}): {question_text} [targets: {tag_key}]"
                )

            return {
                "questions": all_questions,
                "distribution": question_distribution,
                "by_category": questions_by_category,
                "total_questions": len(all_questions),
                "question_limit": question_limit,
                "success": True,
            }

        except Exception as e:
            logger.error(f"Failed to generate questions in single call: {str(e)}")
            # Fallback to original method
            logger.info("Falling back to sequential category generation")
            return await self._generate_gap_targeted_questions(
                cv_assessment_id,
                gap_analysis,
                job_description,
                question_limit,
                category_weights,
            )

    def _clear_request_cache(self):
        """Clear request-level cache - call at start of new request"""
        self._request_cache = {
            "candidate_tags": {},
            "cv_assessments": {},
        }

    def _get_cached_candidate_tags(
        self, cv_assessment_id: UUID
    ) -> List[Dict[str, Any]]:
        """Get candidate tags with caching to eliminate redundant queries"""
        cache_key = str(cv_assessment_id)

        if cache_key not in self._request_cache["candidate_tags"]:
            logger.info(f"Loading candidate tags for question generation (cache miss)")
            tags_data = self.db_service.get_candidate_tags(cache_key)
            self._request_cache["candidate_tags"][cache_key] = tags_data
        else:
            logger.info(f"Using cached candidate tags for question generation")

        return self._request_cache["candidate_tags"][cache_key]

    def _get_cached_cv_assessment(
        self, cv_assessment_id: UUID
    ) -> Optional[Dict[str, Any]]:
        """Get CV assessment with caching to eliminate redundant queries"""
        cache_key = str(cv_assessment_id)

        if cache_key not in self._request_cache["cv_assessments"]:
            logger.info(f"Loading CV assessment for question generation (cache miss)")
            assessment_data = self.db_service.get_cv_assessment_by_id(
                int(cv_assessment_id)
            )
            self._request_cache["cv_assessments"][cache_key] = assessment_data
        else:
            logger.info(f"Using cached CV assessment for question generation")

        return self._request_cache["cv_assessments"][cache_key]

    def _build_optimized_single_call_prompt(
        self,
        all_categories_info: List[str],
        already_extracted_tags: List[str],
        qa_history: List[Dict[str, Any]],
        job_description: Optional[str],
        total_questions: int,
        question_distribution: Dict[str, int],
        cv_text: str = "",
    ) -> str:
        """Build optimized prompt for single API call - IMPROVED with CV context and job qualifications question"""

        # Extract covered topics from Q&A history
        covered_topics = []
        for qa in qa_history:
            if qa.get("question") and qa.get("answer"):
                covered_topics.append(f"Q: {qa['question'][:80]}...")

        covered_context = (
            "\n".join(covered_topics) if covered_topics else "No previous questions."
        )
        extracted_context = (
            "\n".join(already_extracted_tags)
            if already_extracted_tags
            else "No tags extracted yet."
        )

        # Include CV context for better question quality
        cv_context = ""
        if cv_text:
            cv_context = f"""
CV CONTENT (for context):
{cv_text[:1500]}...

"""

        # Build distribution summary for category-based questions
        distribution_summary = []
        category_questions_total = 0
        for category, count in question_distribution.items():
            if count > 0:
                distribution_summary.append(f"- {category.upper()}: {count} questions")
                category_questions_total += count

        # Job qualifications question instruction
        job_qualifications_instruction = f"""
MANDATORY FIRST QUESTION - JOB QUALIFICATIONS:
Generate 1 job qualifications question as the FIRST question that asks about ALL relevant professional qualifications, licenses, certifications, or requirements based on the candidate's field/role from their CV. This should be a comprehensive question covering multiple qualifications in one question.

Examples:
- For a driver: "Do you have a valid driver's license, CDL certification, and clean driving record?"
- For a doctor: "Do you have your medical degree, board certification, and current medical license?"
- For a software engineer: "Do you have relevant programming certifications, degree in computer science, and any cloud platform certifications?"
- For a teacher: "Do you have your teaching degree, teaching license, and any specialized certifications?"

This question should:
- Cover ALL major qualifications for their profession in ONE question
- Be answerable with a simple yes/no plus brief details
- Use "job_qualifications" as the category
- Use "professional_qualifications" as the tag_key
- Have weight 10 (highest priority)
"""

        prompt = f"""You are an expert interviewer generating {total_questions} targeted interview questions based on CV analysis.

{cv_context}{job_qualifications_instruction}

REMAINING QUESTIONS DISTRIBUTION ({category_questions_total} questions):
{chr(10).join(distribution_summary)}

CATEGORY DETAILS:
{chr(10).join(all_categories_info)}

INSTRUCTIONS:
1. Generate EXACTLY {total_questions} questions total:
   - FIRST question: Job qualifications question (mandatory)
   - Remaining {category_questions_total} questions: Follow the distribution above
2. Make questions DIRECT and CONCISE - answerable in 1-2 sentences
3. Target missing information gaps, not already extracted data
4. Keep questions SHORT and SPECIFIC - avoid long explanations or context
5. Each question should be answerable quickly via chat/text
6. Use simple, direct language - avoid complex or multi-part questions
7. Focus on facts, numbers, or brief descriptions
8. The job qualifications question does NOT follow category rules - it should ask about ALL qualifications in one comprehensive question

QUESTION STYLE EXAMPLES:
- "How many years of experience do you have?"
- "What's your GPA?"
- "Which programming languages do you use most?"
- "What was your team size on recent projects?"
- "Do you have any certifications?"

ALREADY EXTRACTED (don't ask about these):
{extracted_context}

ALREADY COVERED (don't repeat):
{covered_context}

{f"JOB CONTEXT: {job_description}" if job_description else ""}

CRITICAL REQUIREMENTS:
- Questions must be SHORT (under 15 words when possible)
- Questions must be DIRECT and answerable quickly
- Avoid long context or explanations in questions
- Focus on getting specific facts or brief descriptions
- Use simple, conversational language
- FIRST question must be about job qualifications with category "job_qualifications"

Return EXACTLY {total_questions} questions in valid JSON format:
[
  {{"question": "Do you have [relevant qualifications for their profession]?", "category": "job_qualifications", "tag_key": "professional_qualifications", "source": "ai"}},
  {{"question": "How many years of experience do you have?", "category": "experience", "tag_key": "total_years", "source": "ai"}},
  {{"question": "What's your GPA?", "category": "education", "tag_key": "gpa", "source": "ai"}},
  ...
]

Generate all {total_questions} SHORT, DIRECT questions now:"""

        return prompt

    def _parse_single_call_response(
        self, response: str, question_distribution: Dict[str, int]
    ) -> List[Dict[str, Any]]:
        """Parse AI response from single call to extract all questions - IMPROVED ROBUSTNESS"""
        try:
            logger.info(f"Parsing response of length: {len(response)}")

            # Multiple parsing strategies for robustness
            questions = []

            # Strategy 1: Try to find complete JSON array
            json_patterns = [
                r"\[\s*\{.*?\}\s*(?:,\s*\{.*?\}\s*)*\]",  # Complete array
                r"\[[\s\S]*?\]",  # Any content between brackets
                r"\{.*?\}(?:\s*,\s*\{.*?\})*",  # Multiple objects without brackets
            ]

            for pattern in json_patterns:
                json_match = re.search(pattern, response, re.DOTALL)
                if json_match:
                    json_text = json_match.group()

                    # Ensure it's wrapped in array brackets
                    if not json_text.strip().startswith("["):
                        json_text = f"[{json_text}]"

                    try:
                        questions = json.loads(json_text)
                        logger.info(
                            f"Successfully parsed JSON with pattern: {pattern[:20]}..."
                        )
                        break
                    except json.JSONDecodeError as e:
                        logger.warning(
                            f"JSON decode failed for pattern {pattern[:20]}...: {str(e)}"
                        )
                        continue

            # Strategy 2: If JSON parsing fails, try line-by-line extraction
            if not questions:
                logger.info("JSON parsing failed, trying line-by-line extraction")
                questions = self._extract_questions_from_text(response)

            # Validate and clean questions
            validated_questions = []
            expected_total = sum(question_distribution.values())

            for i, q in enumerate(questions):
                if isinstance(q, dict) and "question" in q and q["question"].strip():
                    # Ensure all required fields are present
                    q["category"] = q.get("category", "general")
                    q["tag_key"] = q.get("tag_key", "general")
                    q["source"] = q.get("source", "ai")

                    # Add metadata
                    q["metadata"] = {
                        "category": q["category"],
                        "tag_key": q["tag_key"],
                        "source": q["source"],
                    }

                    validated_questions.append(q)
                elif isinstance(q, str) and q.strip():
                    # Handle string questions
                    validated_questions.append(
                        {
                            "question": q.strip(),
                            "category": "general",
                            "tag_key": "general",
                            "source": "ai",
                            "metadata": {
                                "category": "general",
                                "tag_key": "general",
                                "source": "ai",
                            },
                        }
                    )

            logger.info(
                f"Parsed {len(validated_questions)} questions from single API call (expected: {expected_total})"
            )

            # If we got significantly fewer questions than expected, log the issue
            if len(validated_questions) < expected_total * 0.8:
                logger.warning(
                    f"Parsed fewer questions than expected: {len(validated_questions)}/{expected_total}"
                )
                logger.warning(f"Response preview: {response[:500]}...")

            return validated_questions

        except Exception as e:
            logger.error(f"Failed to parse single call response: {str(e)}")
            logger.error(f"Response preview: {response[:200]}...")
            return []

    def _extract_questions_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract questions from text when JSON parsing fails"""
        questions = []

        # Look for question patterns
        question_patterns = [
            r"(?:Question|Q)\s*\d*[:\-]?\s*(.+?\?)",
            r'"question"\s*:\s*"([^"]+\?)"',
            r"(\w+.*?\?)",  # Any sentence ending with ?
        ]

        for pattern in question_patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 10:  # Filter out very short matches
                    questions.append(
                        {
                            "question": match.strip(),
                            "category": "general",
                            "tag_key": "general",
                            "source": "ai",
                        }
                    )

        # Remove duplicates
        seen = set()
        unique_questions = []
        for q in questions:
            if q["question"] not in seen:
                seen.add(q["question"])
                unique_questions.append(q)

        return unique_questions[:20]  # Limit to reasonable number

    async def _generate_gap_targeted_questions(
        self,
        cv_assessment_id: UUID,
        gap_analysis: Dict[str, Any],
        job_description: Optional[str] = None,
        question_limit: int = 15,
        category_weights: Dict[str, float] = None,
    ) -> Dict[str, Any]:
        """
        Generate questions targeted at filling coverage gaps.

        Args:
            cv_assessment_id: The assessment ID
            gap_analysis: Gap analysis from tag extraction service
            job_description: Optional job description for context
            question_limit: Maximum number of questions to generate
            category_weights: Weights for each category

        Returns:
            Dictionary with generated questions and metadata
        """
        try:
            # Ensure we have category weights
            weights = category_weights or self.default_category_weights

            # Calculate question distribution based on gaps and weights
            question_distribution = self._calculate_question_distribution(
                gap_analysis, weights, question_limit
            )

            # Only include salary question if it has a non-zero weight
            if weights.get("salary", 0) > 0 and "salary" not in question_distribution:
                question_distribution["salary"] = 1

            logger.info(f"Question distribution: {question_distribution}")
            logger.info(f"Using category weights: {weights}")

            # Generate questions for each category
            all_questions = []
            questions_by_category = {}

            for category_name, num_questions in question_distribution.items():
                if num_questions <= 0:
                    continue

                logger.info(
                    f"Generating {num_questions} questions for category '{category_name}'"
                )

                # Get missing tags for this category
                missing_tags = []
                if category_name in gap_analysis.get("missing_tags", {}):
                    all_missing_tags = gap_analysis["missing_tags"][category_name]

                    # Filter out tags that are already extracted (double-check against database)
                    try:
                        # OPTIMIZATION: Use cached candidate tags instead of fresh database query
                        existing_tags = self._get_cached_candidate_tags(
                            cv_assessment_id
                        )
                        extracted_tag_keys = set()
                        if existing_tags:
                            for tag in existing_tags:
                                tag_def = tag.get("tag_definitions", {})
                                tag_key = tag_def.get("tag_key", "")
                                tag_value = tag.get("value", "")
                                # Only consider tags with meaningful values as extracted
                                if (
                                    tag_key
                                    and tag_value
                                    and tag_value.strip()
                                    and tag_value.strip().lower()
                                    not in ["", "none", "null", "n/a", "not applicable"]
                                ):
                                    extracted_tag_keys.add(tag_key)

                        # Filter missing tags to exclude already extracted ones
                        missing_tags = [
                            tag
                            for tag in all_missing_tags
                            if tag.get("tag_key") not in extracted_tag_keys
                        ]

                        logger.info(
                            f"Gap analysis found {len(all_missing_tags)} missing tags for category '{category_name}'"
                        )
                        logger.info(
                            f"After filtering already-extracted tags: {len(missing_tags)} missing tags remain"
                        )
                        if len(all_missing_tags) > len(missing_tags):
                            filtered_out = [
                                tag.get("tag_key")
                                for tag in all_missing_tags
                                if tag.get("tag_key") in extracted_tag_keys
                            ]
                            logger.info(
                                f"Filtered out already-extracted tags: {filtered_out}"
                            )

                    except Exception as e:
                        logger.warning(
                            f"Could not filter already-extracted tags: {str(e)}"
                        )
                        missing_tags = gap_analysis["missing_tags"][category_name]
                        logger.info(
                            f"Using unfiltered missing tags: {len(missing_tags)} tags for category '{category_name}'"
                        )

                # Generate questions for this category
                category_questions = await self._generate_category_questions(
                    cv_assessment_id,
                    category_name,
                    missing_tags,
                    job_description,
                    num_questions,
                )

                if category_questions:
                    # Log each generated question with its category and tag info
                    for i, q in enumerate(category_questions):
                        logger.info(
                            f"Generated question {i+1} for category '{category_name}': {q.get('question', '')[:100]}..."
                        )
                        logger.info(f"  - Targets tag: {q.get('tag_key', 'general')}")
                        logger.info(f"  - Source: {q.get('source', 'unknown')}")

                        # Add metadata to the question for better tracking
                        q["metadata"] = {
                            "category": category_name,
                            "tag_key": q.get("tag_key", "general"),
                            "source": q.get("source", "unknown"),
                        }

                    all_questions.extend(category_questions)
                    questions_by_category[category_name] = category_questions

            # Ensure we don't exceed the question limit
            all_questions = all_questions[:question_limit]

            return {
                "questions": all_questions,
                "distribution": question_distribution,
                "by_category": questions_by_category,
                "total_questions": len(all_questions),
                "question_limit": question_limit,
                "success": True,
            }

        except Exception as e:
            logger.error(f"Failed to generate gap-targeted questions: {str(e)}")
            return {"questions": [], "error": str(e), "success": False}

    def _calculate_question_distribution(
        self,
        gap_analysis: Dict[str, Any],
        category_weights: Dict[str, float],
        question_limit: int,
    ) -> Dict[str, int]:
        """
        Calculate how many questions to allocate to each category based on category weights.
        Only categories with non-zero weights will receive questions.

        Args:
            gap_analysis: Gap analysis from tag extraction service (used for targeting specific tags)
            category_weights: Weights for each category (PRIMARY factor for distribution)
            question_limit: Maximum number of questions

        Returns:
            Dictionary with category names and number of questions to generate
        """
        logger.info(
            f"Calculating question distribution with weights: {category_weights}"
        )

        # Filter category weights to only include categories with non-zero weights
        active_weights = {
            cat: weight for cat, weight in category_weights.items() if weight > 0
        }
        logger.info(f"Active categories (non-zero weights): {active_weights}")

        if not active_weights:
            logger.warning("No categories have non-zero weights!")
            return {}

        # Create distribution based PURELY on category weights (ignore gap analysis for distribution)
        distribution = {}
        total_weight = sum(active_weights.values())

        for category, weight in active_weights.items():
            # Calculate questions for this category based on its weight
            cat_questions = max(1, int((weight / total_weight) * question_limit))
            distribution[category] = cat_questions
            logger.info(
                f"Allocated {cat_questions} questions to category '{category}' (weight: {weight:.2f})"
            )

        # Adjust to match exact question limit
        total_allocated = sum(distribution.values())

        if total_allocated > question_limit:
            # Remove questions from categories with the most questions
            while total_allocated > question_limit:
                max_category = max(distribution.items(), key=lambda x: x[1])[0]
                if distribution[max_category] > 1:
                    distribution[max_category] -= 1
                    total_allocated -= 1
                else:
                    break

        elif total_allocated < question_limit:
            # Add questions to categories with the highest weights
            remaining = question_limit - total_allocated
            sorted_categories = sorted(
                active_weights.items(), key=lambda x: x[1], reverse=True
            )

            for i in range(remaining):
                category = sorted_categories[i % len(sorted_categories)][0]
                distribution[category] += 1

        # Final verification that distribution matches question limit exactly
        final_total = sum(distribution.values())
        if final_total != question_limit:
            logger.warning(
                f"Distribution total {final_total} != question_limit {question_limit}, final adjustment needed"
            )
            distribution = self._adjust_distribution_to_limit(
                distribution, question_limit
            )

        logger.info(f"Final question distribution: {distribution}")
        return distribution

    async def _generate_category_questions(
        self,
        cv_assessment_id: UUID,
        category_name: str,
        missing_tags: List[Dict[str, Any]],
        job_description: Optional[str] = None,
        num_questions: int = 3,
    ) -> List[Dict[str, Any]]:
        """
        Generate questions for a specific category targeting missing tags.

        Args:
            cv_assessment_id: The assessment ID
            category_name: Category name
            missing_tags: List of missing tags in this category
            job_description: Optional job description for context
            num_questions: Number of questions to generate

        Returns:
            List of generated questions with metadata
        """
        try:

            # Extract tag keys and question templates from missing tags
            tag_info = []
            for tag in missing_tags:
                tag_key = tag.get("tag_key")
                display_name = tag.get("display_name")
                questions = tag.get("questions", [])

                tag_info.append(
                    {
                        "tag_key": tag_key,
                        "display_name": display_name,
                        "questions": questions,
                        "is_required": tag.get("is_required", False),
                        "weight": tag.get("weight", 1.0),
                    }
                )

            # Sort tags by weight and required status
            tag_info.sort(key=lambda x: (not x["is_required"], -x["weight"]))

            # ALWAYS use AI generation instead of templates
            # Build prompt for AI to generate questions
            prompt = self._build_question_generation_prompt(
                cv_assessment_id,
                category_name,
                tag_info,
                job_description,
                num_questions,
            )

            # Generate questions using Gemini
            response = self.model.generate_content(prompt)

            # Parse AI response to extract questions
            ai_questions = self._parse_question_response(response.text, category_name)

            # Use only AI-generated questions
            all_questions = ai_questions[:num_questions]

            # Ensure we don't exceed the requested number of questions
            return all_questions[:num_questions]

        except Exception as e:
            logger.error(
                f"Failed to generate questions for category {category_name}: {str(e)}"
            )
            return []

    def _build_question_generation_prompt(
        self,
        cv_assessment_id: UUID,
        category_name: str,
        tag_info: List[Dict[str, Any]],
        job_description: Optional[str] = None,
        num_questions: int = 3,
    ) -> str:
        """
        Build optimized prompt for AI to generate questions for a specific category.
        OPTIMIZED: Removed CV text to reduce prompt size and improve performance.

        Args:
            cv_assessment_id: Assessment ID for context
            category_name: Category name
            tag_info: Information about missing tags
            job_description: Optional job description for context
            num_questions: Number of questions to generate

        Returns:
            Optimized prompt for AI
        """
        # Extract tag keys and display names
        tag_keys = [f"{tag['tag_key']} ({tag['display_name']})" for tag in tag_info]

        # Build prompt
        # Get current assessment data to avoid asking about already-answered topics
        # OPTIMIZATION: Use cached assessment data instead of fresh database query
        assessment_data = self._get_cached_cv_assessment(cv_assessment_id)
        qa_history = []
        if assessment_data and assessment_data.get("assessment_state"):
            try:
                state_data = json.loads(assessment_data["assessment_state"])
                if isinstance(state_data, dict):
                    qa_history = state_data.get("qa_history", [])
                elif isinstance(state_data, list):
                    qa_history = state_data
            except:
                qa_history = []

        # Extract topics already covered in current session
        covered_topics = []
        for qa in qa_history:
            if qa.get("question") and qa.get("answer"):
                covered_topics.append(
                    f"Q: {qa['question'][:100]}... A: {qa['answer'][:100]}..."
                )

        covered_context = (
            "\n".join(covered_topics)
            if covered_topics
            else "No previous questions in this session."
        )

        # Get already-extracted tags to avoid asking about them
        already_extracted_tags = []
        try:
            # OPTIMIZATION: Use cached candidate tags instead of fresh database query
            existing_tags = self._get_cached_candidate_tags(cv_assessment_id)
            if existing_tags:
                logger.info(
                    f"Found {len(existing_tags)} existing tags for assessment {cv_assessment_id}"
                )
                for tag in existing_tags:
                    tag_def = tag.get("tag_definitions", {})
                    tag_key = tag_def.get("tag_key", "")
                    display_name = tag_def.get("display_name", "")
                    tag_value = tag.get("value", "")
                    if tag_key and tag_value:
                        already_extracted_tags.append(
                            f"{tag_key} ({display_name}): {tag_value}"
                        )
                        logger.info(f"Already extracted: {tag_key} = {tag_value}")
            else:
                logger.info(f"No existing tags found for assessment {cv_assessment_id}")
        except Exception as e:
            logger.warning(f"Could not get existing tags: {str(e)}")

        extracted_tags_context = (
            "\n".join(already_extracted_tags)
            if already_extracted_tags
            else "No tags extracted yet."
        )

        logger.info(
            f"Extracted tags context for prompt: {extracted_tags_context[:200]}..."
        )

        prompt = f"""Generate {num_questions} SHORT, DIRECT {category_name} questions for missing information.

TARGET TAGS: {', '.join(tag_keys)}

REQUIREMENTS:
- Questions must be CONCISE (under 15 words when possible)
- Questions must be answerable in 1-2 sentences
- Use simple, direct language
- Focus on facts, numbers, or brief descriptions
- Don't ask about extracted info or covered topics

EXAMPLES:
- "How many years of experience do you have?"
- "What's your GPA?"
- "Which certifications do you hold?"
- "What was your team size?"

ALREADY EXTRACTED (don't ask about these):
{extracted_tags_context}

ALREADY COVERED (don't repeat):
{covered_context}

{f"JOB CONTEXT: {job_description}" if job_description else ""}

Return JSON: [{{"question": "short direct question?", "category": "{category_name}", "tag_key": "...", "source": "ai"}}]"""
        return prompt

    def _parse_question_response(
        self, response: str, category_name: str
    ) -> List[Dict[str, Any]]:
        """
        Parse AI response to extract generated questions.

        Args:
            response: AI response text
            category_name: Category name for fallback

        Returns:
            List of question dictionaries
        """
        try:
            # Try to extract JSON from the response
            import re
            import json

            # Find JSON array in response
            json_match = re.search(r"\[\s*\{.*\}\s*\]", response, re.DOTALL)

            if json_match:
                questions = json.loads(json_match.group())

                # Validate each question has required fields
                validated_questions = []
                for q in questions:
                    if "question" in q:
                        # Ensure all required fields are present
                        q["category"] = q.get("category", category_name)
                        q["tag_key"] = q.get("tag_key", "general")
                        q["source"] = q.get("source", "ai")
                        validated_questions.append(q)

                return validated_questions
            else:
                # Fallback: try to extract questions line by line
                questions = []
                for line in response.split("\n"):
                    line = line.strip()
                    if line and ("?" in line or ":" in line):
                        # Extract the question part
                        question_text = line
                        if ":" in line:
                            question_text = line.split(":", 1)[1].strip()

                        questions.append(
                            {
                                "question": question_text,
                                "category": category_name,
                                "tag_key": "general",
                                "source": "fallback",
                            }
                        )

                return questions

        except Exception as e:
            logger.error(f"Failed to parse question response: {str(e)}")
            return []

    async def _generate_default_questions(
        self,
        cv_assessment_id: UUID,
        job_description: Optional[str] = None,
        question_limit: int = 15,
        category_weights: Dict[str, float] = None,
    ) -> Dict[str, Any]:
        """
        Generate default questions when gap analysis is not available.

        Args:
            cv_assessment_id: The assessment ID
            job_description: Optional job description for context
            question_limit: Maximum number of questions to generate
            category_weights: Weights for each category

        Returns:
            Dictionary with generated questions and metadata
        """
        try:
            # Ensure we have category weights
            weights = category_weights or self.default_category_weights

            # Calculate question distribution based on weights
            total_weight = sum(weights.values())
            distribution = {}

            for category, weight in weights.items():
                num_questions = max(1, int((weight / total_weight) * question_limit))
                distribution[category] = num_questions

            # Adjust to match question limit
            total_questions = sum(distribution.values())
            if total_questions > question_limit:
                # Remove questions from categories with the most questions
                while total_questions > question_limit:
                    max_category = max(distribution.items(), key=lambda x: x[1])[0]
                    distribution[max_category] -= 1
                    total_questions -= 1
            elif total_questions < question_limit:
                # Add questions to categories with the highest weights
                sorted_categories = sorted(
                    weights.items(), key=lambda x: x[1], reverse=True
                )
                i = 0
                while total_questions < question_limit:
                    category = sorted_categories[i % len(sorted_categories)][0]
                    distribution[category] += 1
                    total_questions += 1
                    i += 1

            # Generate questions for each category
            all_questions = []
            questions_by_category = {}

            for category_name, num_questions in distribution.items():
                if num_questions <= 0:
                    continue

                # Generate questions for this category
                category_questions = await self._generate_default_category_questions(
                    cv_assessment_id, category_name, job_description, num_questions
                )

                if category_questions:
                    all_questions.extend(category_questions)
                    questions_by_category[category_name] = category_questions

            # Ensure we don't exceed the question limit
            all_questions = all_questions[:question_limit]

            return {
                "questions": all_questions,
                "distribution": distribution,
                "by_category": questions_by_category,
                "total_questions": len(all_questions),
                "question_limit": question_limit,
                "success": True,
            }

        except Exception as e:
            logger.error(f"Failed to generate default questions: {str(e)}")
            return {"questions": [], "error": str(e), "success": False}

    async def generate_followup_questions(
        self,
        cv_assessment_id: UUID,
        qa_history: List[Dict[str, Any]],
        count: int = 1,
    ) -> Dict[str, Any]:
        """
        Generate follow-up questions based on previous Q&A history.
        Uses the same consistent prompt and logic as the main question generation.

        Args:
            cv_assessment_id: The assessment ID
            qa_history: Previous Q&A history
            count: Number of follow-up questions to generate

        Returns:
            Dictionary with generated follow-up questions and metadata
        """
        try:
            logger.info(
                f"Generating {count} follow-up questions for assessment {cv_assessment_id}"
            )

            # Get CV text for context
            cv_text = ""
            assessment_data = self.db_service.get_cv_assessment_by_id(
                int(cv_assessment_id)
            )
            if assessment_data:
                cv_text = assessment_data.get("pdf_text", "")

            # Extract topics already covered in current session
            covered_topics = []
            for qa in qa_history:
                if qa.get("question") and qa.get("answer"):
                    covered_topics.append(
                        f"Q: {qa['question'][:100]}... A: {qa['answer'][:100]}..."
                    )

            covered_context = (
                "\n".join(covered_topics)
                if covered_topics
                else "No previous questions in this session."
            )

            # Build follow-up prompt
            prompt = f"""
You are an expert recruiter conducting a follow-up interview. Based on the previous Q&A session, generate {count} intelligent follow-up questions.

CRITICAL REQUIREMENTS:
1. Generate exactly {count} follow-up questions
2. Questions must be SHORT and DIRECT (under 15 words when possible)
3. Questions must be answerable in 1-2 sentences
4. DO NOT repeat questions already asked
5. Focus on getting specific facts, numbers, or brief clarifications
6. Use simple, conversational language
7. Build on previous answers to fill information gaps

QUESTION STYLE EXAMPLES:
- "How many people were on that team?"
- "What was the project timeline?"
- "Which technologies did you use?"
- "What was your specific role?"

Previous Q&A Session:
{covered_context}

CV Context:
{cv_text[:2000]}...

Generate exactly {count} SHORT, DIRECT follow-up questions in JSON format:
[
  {{
    "question": "How many people were on that team?",
    "category": "followup",
    "tag_key": "followup",
    "source": "ai"
  }}
]

IMPORTANT: 
- Generate exactly {count} questions
- Build on previous conversation
- Keep questions concise and direct
- Focus on getting specific details or examples
"""

            # Generate questions using Gemini
            response = self.model.generate_content(prompt)

            # Parse AI response to extract questions
            ai_questions = self._parse_question_response(response.text, "followup")

            # Ensure we don't exceed the requested count
            final_questions = ai_questions[:count]

            return {
                "questions": final_questions,
                "count": len(final_questions),
                "success": True,
            }

        except Exception as e:
            logger.error(f"Failed to generate follow-up questions: {str(e)}")
            return {"questions": [], "error": str(e), "success": False}

    def _adjust_distribution_to_limit(
        self, distribution: Dict[str, int], target_limit: int
    ) -> Dict[str, int]:
        """Adjust question distribution to match exact target limit"""
        current_total = sum(distribution.values())

        if current_total == target_limit:
            return distribution

        adjusted = distribution.copy()

        if current_total > target_limit:
            # Remove questions from categories with most questions
            excess = current_total - target_limit
            while excess > 0:
                max_category = max(adjusted.items(), key=lambda x: x[1])[0]
                if adjusted[max_category] > 1:
                    adjusted[max_category] -= 1
                    excess -= 1
                else:
                    break
        else:
            # Add questions to categories with highest weights
            deficit = target_limit - current_total
            # Sort by original weights if available
            sorted_categories = sorted(
                adjusted.keys(), key=lambda x: adjusted[x], reverse=True
            )

            for i in range(deficit):
                category = sorted_categories[i % len(sorted_categories)]
                adjusted[category] += 1

        logger.info(f"Adjusted distribution from {distribution} to {adjusted}")
        return adjusted

    def _pad_questions_to_limit(
        self,
        questions: List[Dict[str, Any]],
        target_limit: int,
        distribution: Dict[str, int],
    ) -> List[Dict[str, Any]]:
        """Pad questions list to reach target limit with generic questions"""
        if len(questions) >= target_limit:
            return questions

        needed = target_limit - len(questions)
        logger.info(f"Padding {needed} questions to reach target of {target_limit}")

        # Generic questions to pad with
        generic_questions = [
            "Can you tell me more about your professional experience?",
            "What are your key strengths in your field?",
            "How do you handle challenging situations at work?",
            "What motivates you in your career?",
            "Can you describe a project you're particularly proud of?",
            "How do you stay updated with industry trends?",
            "What are your career goals for the next few years?",
            "How do you approach problem-solving?",
            "Can you give an example of your leadership experience?",
            "What technical skills are you most confident in?",
        ]

        padded_questions = questions.copy()

        for i in range(needed):
            if i < len(generic_questions):
                question_text = generic_questions[i]
            else:
                question_text = f"Can you elaborate on your experience and qualifications? (Question {len(padded_questions) + 1})"

            padded_questions.append(
                {
                    "question": question_text,
                    "category": "general",
                    "tag_key": "general",
                    "source": "ai_padded",
                    "metadata": {
                        "category": "general",
                        "tag_key": "general",
                        "source": "ai_padded",
                    },
                }
            )

        return padded_questions

    async def _generate_default_category_questions(
        self,
        cv_assessment_id: UUID,
        category_name: str,
        job_description: Optional[str],
        num_questions: int,
    ) -> List[Dict[str, Any]]:
        """Generate default questions for a category when gap analysis is not available"""
        try:
            # Get CV text for context
            assessment_data = self._get_cached_cv_assessment(cv_assessment_id)
            cv_text = ""
            if assessment_data:
                cv_text = assessment_data.get("pdf_text", "")

            prompt = f"""Generate {num_questions} SHORT, DIRECT {category_name} questions.

REQUIREMENTS:
- Questions must be CONCISE (under 15 words when possible)
- Questions must be answerable in 1-2 sentences
- Use simple, direct language
- Focus on facts, numbers, or brief descriptions

EXAMPLES:
- "How many years of experience do you have?"
- "What's your GPA?"
- "Which programming languages do you know?"
- "What was your team size?"

{f"JOB CONTEXT: {job_description}" if job_description else ""}

CV CONTEXT (for reference):
{cv_text[:1000]}...

Return JSON: [{{"question": "short direct question?", "category": "{category_name}", "tag_key": "general", "source": "ai"}}]"""

            response = self.model.generate_content(prompt)
            questions = self._parse_question_response(response.text, category_name)

            return questions[:num_questions]

        except Exception as e:
            logger.error(
                f"Failed to generate default questions for {category_name}: {str(e)}"
            )
            return []


def create_tool(agent):
    """Return a LangChain tool for question generation bound to the given agent instance."""

    @tool
    async def generate_assessment_questions(
        cv_assessment_id: str,
        job_id: Optional[str] = None,
        question_limit: Optional[int] = None,
        category_weights: Optional[Dict[str, float]] = None,
    ) -> Dict[str, Any]:
        """Generate intelligent questions for CV assessment based on gap analysis.

        Uses the QuestionGenerationService to create targeted questions that fill
        coverage gaps in the candidate's CV relative to job requirements.

        Args:
            cv_assessment_id: The CV assessment ID
            job_id: Optional job ID for context-specific questions
            question_limit: Maximum number of questions to generate (default: 15)
            category_weights: Optional custom weights for question categories

        Returns:
            Dict containing:
                - questions: List of generated questions with metadata
                - distribution: How questions were distributed across categories
                - total_questions: Number of questions generated
                - success: Whether generation was successful
        """
        try:
            logger.info(f"Generating questions for CV assessment {cv_assessment_id}")

            # Create question generation service instance
            question_service = QuestionGenerationService(agent.db_service)

            # Convert string IDs to UUIDs
            cv_uuid = UUID(cv_assessment_id)
            job_uuid = UUID(job_id) if job_id else None

            # Get job description if job_id provided
            job_description = None
            if job_uuid:
                try:
                    job_data = agent.db_service.get_job(job_uuid)
                    if job_data:
                        job_description = job_data.get("description")
                except Exception as e:
                    logger.warning(f"Could not get job description: {str(e)}")

            # Generate questions using the service
            result = await question_service.generate_questions(
                cv_assessment_id=cv_uuid,
                job_description=job_description,
                question_limit=question_limit,
                category_weights=category_weights,
            )

            if result.get("success", False):
                logger.info(
                    f"Successfully generated {len(result.get('questions', []))} questions"
                )

                # Log question distribution for transparency
                distribution = result.get("distribution", {})
                logger.info(f"Question distribution: {distribution}")

                return result
            else:
                logger.error(
                    f"Question generation failed: {result.get('error', 'Unknown error')}"
                )
                return result

        except Exception as exc:
            logger.error(f"Error generating questions: {str(exc)}", exc_info=True)
            return {
                "questions": [],
                "error": str(exc),
                "success": False,
            }

    @tool
    async def generate_followup_questions(
        cv_assessment_id: str,
        qa_history: List[Dict[str, Any]],
        count: int = 1,
    ) -> Dict[str, Any]:
        """Generate follow-up questions based on previous Q&A history.

        Uses the same QuestionGenerationService for consistency.

        Args:
            cv_assessment_id: The CV assessment ID
            qa_history: Previous question and answer history
            count: Number of follow-up questions to generate

        Returns:
            Dict containing generated follow-up questions
        """
        try:
            logger.info(
                f"Generating {count} follow-up questions for assessment {cv_assessment_id}"
            )

            # Create question generation service instance
            question_service = QuestionGenerationService(agent.db_service)

            # Convert string ID to UUID
            cv_uuid = UUID(cv_assessment_id)

            # Use the dedicated follow-up method from QuestionGenerationService
            # This ensures consistent prompt and logic specifically for follow-ups
            result = await question_service.generate_followup_questions(
                cv_assessment_id=cv_uuid,
                qa_history=qa_history,
                count=count,
            )

            if result.get("success", False):
                logger.info(
                    f"Successfully generated {len(result.get('questions', []))} follow-up questions"
                )
                return {
                    "questions": result.get("questions", []),
                    "count": len(result.get("questions", [])),
                    "success": True,
                }
            else:
                logger.error(
                    f"Follow-up question generation failed: {result.get('error', 'Unknown error')}"
                )
                return result

        except Exception as exc:
            logger.error(
                f"Error generating follow-up questions: {str(exc)}", exc_info=True
            )
            return {
                "questions": [],
                "error": str(exc),
                "success": False,
            }

    return generate_assessment_questions, generate_followup_questions
